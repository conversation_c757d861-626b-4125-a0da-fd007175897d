import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import pyperclip

class BabelDOCGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("BabelDOC 参数配置工具")
        self.root.geometry("1000x800")

        # 读取配置文件
        self.config = self.load_config()
        
        # 创建主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建滚动画布
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        self.create_widgets(scrollable_frame)

    def load_config(self):
        """读取config.txt配置文件"""
        config = {
            'base_url': 'https://api.openai.com/v1',
            'api': '',
            'model': 'gpt-4o-mini'
        }

        try:
            if os.path.exists('config.txt'):
                with open('config.txt', 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and ':' in line:
                            key, value = line.split(':', 1)
                            config[key.strip()] = value.strip()
        except Exception as e:
            print(f"读取配置文件失败: {e}")

        return config

    def create_widgets(self, parent):
        # 1. 文件选择区域
        file_frame = ttk.LabelFrame(parent, text="文件设置", padding="5")
        file_frame.pack(fill=tk.X, pady=5)

        ttk.Label(file_frame, text="输入文件:").grid(row=0, column=0, sticky=tk.W)
        self.file_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_var, width=50)
        self.file_entry.grid(row=0, column=1, padx=5)
        ttk.Button(file_frame, text="选择单个", command=self.browse_file).grid(row=0, column=2)
        ttk.Button(file_frame, text="批量选择", command=self.browse_multiple_files).grid(row=0, column=3, padx=5)

        # 添加文件列表显示区域
        ttk.Label(file_frame, text="已选文件:").grid(row=1, column=0, sticky=tk.NW)
        self.files_listbox = tk.Listbox(file_frame, height=4, width=70)
        self.files_listbox.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky=tk.W+tk.E)
        ttk.Button(file_frame, text="清空列表", command=self.clear_files).grid(row=1, column=3, padx=5, sticky=tk.N)

        # 存储选中的文件列表
        self.selected_files = []
        
        ttk.Label(file_frame, text="页面范围:").grid(row=2, column=0, sticky=tk.W)
        self.pages_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.pages_var, width=30).grid(row=2, column=1, sticky=tk.W, padx=5)
        ttk.Label(file_frame, text="例: 1,2,1-,-3,3-5 (留空翻译所有页面)").grid(row=2, column=2, columnspan=2, sticky=tk.W)
        
        # 2. 基本设置
        basic_frame = ttk.LabelFrame(parent, text="基本设置", padding="5")
        basic_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(basic_frame, text="源语言:").grid(row=0, column=0, sticky=tk.W)
        self.lang_in_var = tk.StringVar(value="en")
        lang_in_combo = ttk.Combobox(basic_frame, textvariable=self.lang_in_var, width=10)
        lang_in_combo['values'] = ("en", "zh", "ja", "ko", "fr", "de", "es", "ru", "ar")
        lang_in_combo.grid(row=0, column=1, padx=5)
        
        ttk.Label(basic_frame, text="目标语言:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        self.lang_out_var = tk.StringVar(value="zh")
        lang_out_combo = ttk.Combobox(basic_frame, textvariable=self.lang_out_var, width=10)
        lang_out_combo['values'] = ("zh", "en", "ja", "ko", "fr", "de", "es", "ru", "ar")
        lang_out_combo.grid(row=0, column=3, padx=5)
        
        ttk.Label(basic_frame, text="输出目录:").grid(row=1, column=0, sticky=tk.W)
        self.output_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.output_var, width=40).grid(row=1, column=1, columnspan=2, padx=5)
        ttk.Button(basic_frame, text="浏览", command=self.browse_output).grid(row=1, column=3)
        
        # 3. OpenAI设置
        openai_frame = ttk.LabelFrame(parent, text="OpenAI 设置", padding="5")
        openai_frame.pack(fill=tk.X, pady=5)
        
        self.openai_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(openai_frame, text="使用 OpenAI", variable=self.openai_var).grid(row=0, column=0, sticky=tk.W)
        
        ttk.Label(openai_frame, text="模型:").grid(row=1, column=0, sticky=tk.W)
        self.openai_model_var = tk.StringVar(value=self.config.get('model', 'gpt-4o-mini'))
        model_combo = ttk.Combobox(openai_frame, textvariable=self.openai_model_var, width=25)
        model_combo['values'] = (
            "gpt-4o-mini", "gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo",
            "deepseek-chat", "deepseek-coder", "glm-4-flash", "glm-4-plus",
            "claude-3-haiku", "claude-3-sonnet", "qwen-turbo", "qwen-plus",
            "moonshot-v1-8k", "yi-large", "baichuan2-turbo"
        )
        model_combo.grid(row=1, column=1, padx=5)

        # 添加提示标签
        ttk.Label(openai_frame, text="(可手动输入其他模型名)", font=("Arial", 8), foreground="gray").grid(row=1, column=2, sticky=tk.W, padx=5)
        
        ttk.Label(openai_frame, text="Base URL:").grid(row=2, column=0, sticky=tk.W)
        self.openai_base_url_var = tk.StringVar(value=self.config.get('base_url', 'https://api.openai.com/v1'))
        base_url_combo = ttk.Combobox(openai_frame, textvariable=self.openai_base_url_var, width=50)
        base_url_combo['values'] = (
            "https://tbai.xin/v1",
            "https://api.openai.com/v1",
            "https://api.deepseek.com/v1",
            "https://open.bigmodel.cn/api/paas/v4",
            "https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        base_url_combo.grid(row=2, column=1, columnspan=2, padx=5)
        
        ttk.Label(openai_frame, text="API Key:").grid(row=3, column=0, sticky=tk.W)
        self.openai_api_key_var = tk.StringVar(value=self.config.get('api', ''))
        ttk.Entry(openai_frame, textvariable=self.openai_api_key_var, width=50, show="*").grid(row=3, column=1, columnspan=2, padx=5)
        
        # 4. 兼容性选项
        compat_frame = ttk.LabelFrame(parent, text="兼容性选项", padding="5")
        compat_frame.pack(fill=tk.X, pady=5)
        
        self.enhance_compatibility_var = tk.BooleanVar()
        ttk.Checkbutton(compat_frame, text="启用兼容性增强 (推荐)", variable=self.enhance_compatibility_var).grid(row=0, column=0, sticky=tk.W)
        
        self.skip_clean_var = tk.BooleanVar()
        ttk.Checkbutton(compat_frame, text="跳过 PDF 清理", variable=self.skip_clean_var).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        
        self.dual_translate_first_var = tk.BooleanVar()
        ttk.Checkbutton(compat_frame, text="翻译页面优先", variable=self.dual_translate_first_var).grid(row=1, column=0, sticky=tk.W)
        
        self.disable_rich_text_var = tk.BooleanVar()
        ttk.Checkbutton(compat_frame, text="禁用富文本翻译", variable=self.disable_rich_text_var).grid(row=1, column=1, sticky=tk.W, padx=(20, 0))
        
        # 5. 处理选项
        process_frame = ttk.LabelFrame(parent, text="处理选项", padding="5")
        process_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(process_frame, text="每部分最大页数:").grid(row=0, column=0, sticky=tk.W)
        self.max_pages_var = tk.StringVar()
        ttk.Entry(process_frame, textvariable=self.max_pages_var, width=10).grid(row=0, column=1, padx=5)
        ttk.Label(process_frame, text="(留空不分割)").grid(row=0, column=2, sticky=tk.W)
        
        self.skip_scanned_detection_var = tk.BooleanVar()
        ttk.Checkbutton(process_frame, text="跳过扫描文档检测", variable=self.skip_scanned_detection_var).grid(row=1, column=0, sticky=tk.W)
        
        self.ocr_workaround_var = tk.BooleanVar()
        ttk.Checkbutton(process_frame, text="OCR 解决方法", variable=self.ocr_workaround_var).grid(row=1, column=1, sticky=tk.W, padx=(20, 0))
        
        self.translate_table_text_var = tk.BooleanVar()
        ttk.Checkbutton(process_frame, text="翻译表格文本 (实验性)", variable=self.translate_table_text_var).grid(row=2, column=0, sticky=tk.W)

        self.split_short_lines_var = tk.BooleanVar()
        ttk.Checkbutton(process_frame, text="强制分割短行", variable=self.split_short_lines_var).grid(row=2, column=1, sticky=tk.W, padx=(20, 0))

        ttk.Label(process_frame, text="短行分割因子:").grid(row=3, column=0, sticky=tk.W)
        self.short_line_split_factor_var = tk.StringVar(value="0.8")
        ttk.Entry(process_frame, textvariable=self.short_line_split_factor_var, width=10).grid(row=3, column=1, padx=5)
        ttk.Label(process_frame, text="(默认: 0.8)").grid(row=3, column=2, sticky=tk.W)
        
        # 6. 输出控制
        output_frame = ttk.LabelFrame(parent, text="输出控制", padding="5")
        output_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(output_frame, text="水印模式:").grid(row=0, column=0, sticky=tk.W)
        self.watermark_var = tk.StringVar(value="watermarked")
        watermark_combo = ttk.Combobox(output_frame, textvariable=self.watermark_var, width=15)
        watermark_combo['values'] = ("watermarked", "no_watermark", "both")
        watermark_combo.grid(row=0, column=1, padx=5)
        
        self.no_dual_var = tk.BooleanVar()
        ttk.Checkbutton(output_frame, text="不输出双语PDF", variable=self.no_dual_var).grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        
        self.no_mono_var = tk.BooleanVar()
        ttk.Checkbutton(output_frame, text="不输出单语PDF", variable=self.no_mono_var).grid(row=1, column=0, sticky=tk.W)
        
        # 7. 高级选项
        advanced_frame = ttk.LabelFrame(parent, text="高级选项", padding="5")
        advanced_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(advanced_frame, text="QPS限制:").grid(row=0, column=0, sticky=tk.W)
        self.qps_var = tk.StringVar(value="4")
        ttk.Entry(advanced_frame, textvariable=self.qps_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Label(advanced_frame, text="最小文本长度:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        self.min_text_length_var = tk.StringVar(value="5")
        ttk.Entry(advanced_frame, textvariable=self.min_text_length_var, width=10).grid(row=0, column=3, padx=5)
        
        self.ignore_cache_var = tk.BooleanVar()
        ttk.Checkbutton(advanced_frame, text="忽略缓存", variable=self.ignore_cache_var).grid(row=1, column=0, sticky=tk.W)
        
        self.debug_var = tk.BooleanVar()
        ttk.Checkbutton(advanced_frame, text="调试模式", variable=self.debug_var).grid(row=1, column=1, sticky=tk.W, padx=(20, 0))
        
        # 8. 自定义系统提示
        prompt_frame = ttk.LabelFrame(parent, text="自定义系统提示", padding="5")
        prompt_frame.pack(fill=tk.X, pady=5)
        
        self.custom_prompt_var = tk.StringVar()
        ttk.Entry(prompt_frame, textvariable=self.custom_prompt_var, width=80).pack(fill=tk.X)
        
        # 9. 命令行预览和操作
        cmd_frame = ttk.LabelFrame(parent, text="生成的命令行", padding="5")
        cmd_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 按钮框架
        btn_frame = ttk.Frame(cmd_frame)
        btn_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Button(btn_frame, text="生成命令", command=self.generate_command).pack(side=tk.LEFT)
        ttk.Button(btn_frame, text="复制命令", command=self.copy_command).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="清空设置", command=self.clear_settings).pack(side=tk.LEFT, padx=5)
        
        # 命令显示区域
        self.cmd_text = tk.Text(cmd_frame, height=8, wrap=tk.WORD, font=("Consolas", 10))
        cmd_scrollbar = ttk.Scrollbar(cmd_frame, orient="vertical", command=self.cmd_text.yview)
        self.cmd_text.configure(yscrollcommand=cmd_scrollbar.set)
        
        self.cmd_text.pack(side="left", fill="both", expand=True)
        cmd_scrollbar.pack(side="right", fill="y")
        
        # 绑定变量更新事件
        self.bind_update_events()
        
        # 初始生成命令
        self.generate_command()
    
    def bind_update_events(self):
        """绑定变量更新事件以实时生成命令"""
        variables = [
            self.file_var, self.pages_var, self.lang_in_var, self.lang_out_var,
            self.output_var, self.openai_model_var, self.openai_base_url_var,
            self.openai_api_key_var, self.max_pages_var, self.watermark_var,
            self.qps_var, self.min_text_length_var, self.custom_prompt_var,
            self.short_line_split_factor_var
        ]
        
        for var in variables:
            var.trace_add("write", lambda *args: self.generate_command())
        
        bool_vars = [
            self.openai_var, self.enhance_compatibility_var, self.skip_clean_var,
            self.dual_translate_first_var, self.disable_rich_text_var,
            self.skip_scanned_detection_var, self.ocr_workaround_var,
            self.translate_table_text_var, self.no_dual_var, self.no_mono_var,
            self.ignore_cache_var, self.debug_var, self.split_short_lines_var
        ]
        
        for var in bool_vars:
            var.trace_add("write", lambda *args: self.generate_command())
    
    def browse_file(self):
        filename = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.file_var.set(filename)
            # 清空之前的文件列表，添加单个文件
            self.selected_files = [filename]
            self.update_files_display()

    def browse_multiple_files(self):
        filenames = filedialog.askopenfilenames(
            title="批量选择PDF文件",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filenames:
            # 添加到现有文件列表中（去重）
            for filename in filenames:
                if filename not in self.selected_files:
                    self.selected_files.append(filename)
            self.update_files_display()
            # 更新单文件输入框显示第一个文件
            if self.selected_files:
                self.file_var.set(self.selected_files[0])

    def clear_files(self):
        """清空文件列表"""
        self.selected_files = []
        self.file_var.set("")
        self.update_files_display()

    def update_files_display(self):
        """更新文件列表显示"""
        self.files_listbox.delete(0, tk.END)
        for i, filepath in enumerate(self.selected_files):
            filename = os.path.basename(filepath)
            self.files_listbox.insert(tk.END, f"{i+1}. {filename}")

        # 触发命令更新
        self.generate_command()
    
    def browse_output(self):
        dirname = filedialog.askdirectory(title="选择输出目录")
        if dirname:
            self.output_var.set(dirname)
    
    def generate_command(self):
        cmd_parts = ["babeldoc"]

        # 文件路径 - 支持多文件
        if self.selected_files:
            # 如果有多个文件，使用批量文件参数
            if len(self.selected_files) > 1:
                files_str = '" "'.join(self.selected_files)
                cmd_parts.append(f'--files "{files_str}"')
            else:
                # 单个文件
                cmd_parts.append(f'--files "{self.selected_files[0]}"')
        elif self.file_var.get().strip():
            # 兼容原有的单文件输入
            cmd_parts.append(f'--files "{self.file_var.get()}"')
        
        # 页面范围
        if self.pages_var.get().strip():
            cmd_parts.append(f'--pages "{self.pages_var.get()}"')
        
        # 语言设置
        if self.lang_in_var.get():
            cmd_parts.append(f"--lang-in {self.lang_in_var.get()}")
        if self.lang_out_var.get():
            cmd_parts.append(f"--lang-out {self.lang_out_var.get()}")
        
        # 输出目录
        if self.output_var.get().strip():
            cmd_parts.append(f'--output "{self.output_var.get()}"')
        
        # OpenAI设置
        if self.openai_var.get():
            cmd_parts.append("--openai")
            if self.openai_model_var.get():
                cmd_parts.append(f'--openai-model "{self.openai_model_var.get()}"')
            if self.openai_base_url_var.get():
                cmd_parts.append(f'--openai-base-url "{self.openai_base_url_var.get()}"')
            if self.openai_api_key_var.get():
                cmd_parts.append(f'--openai-api-key "{self.openai_api_key_var.get()}"')
        
        # 兼容性选项
        if self.enhance_compatibility_var.get():
            cmd_parts.append("--enhance-compatibility")
        else:
            if self.skip_clean_var.get():
                cmd_parts.append("--skip-clean")
            if self.dual_translate_first_var.get():
                cmd_parts.append("--dual-translate-first")
            if self.disable_rich_text_var.get():
                cmd_parts.append("--disable-rich-text-translate")
        
        # 处理选项
        if self.max_pages_var.get().strip():
            cmd_parts.append(f"--max-pages-per-part {self.max_pages_var.get()}")
        
        if self.skip_scanned_detection_var.get():
            cmd_parts.append("--skip-scanned-detection")
        
        if self.ocr_workaround_var.get():
            cmd_parts.append("--ocr-workaround")
        
        if self.translate_table_text_var.get():
            cmd_parts.append("--translate-table-text")

        if self.split_short_lines_var.get():
            cmd_parts.append("--split-short-lines")

        if self.short_line_split_factor_var.get() and self.short_line_split_factor_var.get() != "0.8":
            cmd_parts.append(f"--short-line-split-factor {self.short_line_split_factor_var.get()}")
        
        # 输出控制
        if self.watermark_var.get() != "watermarked":
            cmd_parts.append(f"--watermark-output-mode {self.watermark_var.get()}")
        
        if self.no_dual_var.get():
            cmd_parts.append("--no-dual")
        
        if self.no_mono_var.get():
            cmd_parts.append("--no-mono")
        
        # 高级选项
        if self.qps_var.get() and self.qps_var.get() != "4":
            cmd_parts.append(f"--qps {self.qps_var.get()}")
        
        if self.min_text_length_var.get() and self.min_text_length_var.get() != "5":
            cmd_parts.append(f"--min-text-length {self.min_text_length_var.get()}")
        
        if self.ignore_cache_var.get():
            cmd_parts.append("--ignore-cache")
        
        if self.debug_var.get():
            cmd_parts.append("--debug")
        
        # 自定义系统提示
        if self.custom_prompt_var.get().strip():
            cmd_parts.append(f'--custom-system-prompt "{self.custom_prompt_var.get()}"')
        
        # 格式化命令 - 提供两种格式
        # 单行格式（用于直接复制执行）
        single_line_cmd = " ".join(cmd_parts)

        # 多行格式（便于阅读）
        if len(cmd_parts) > 3:
            multi_line_cmd = cmd_parts[0] + " \\\n"
            for part in cmd_parts[1:]:
                multi_line_cmd += f"  {part} \\\n"
            multi_line_cmd = multi_line_cmd.rstrip(" \\\n")
        else:
            multi_line_cmd = single_line_cmd

        # 显示单行格式（更适合复制执行）
        formatted_cmd = single_line_cmd
        
        # 更新命令显示
        self.cmd_text.delete(1.0, tk.END)
        self.cmd_text.insert(1.0, formatted_cmd)
    
    def copy_command(self):
        command = self.cmd_text.get(1.0, tk.END).strip()
        if command:
            try:
                pyperclip.copy(command)
                messagebox.showinfo("成功", "命令已复制到剪贴板！")
            except:
                # 如果pyperclip不可用，使用tkinter的剪贴板
                self.root.clipboard_clear()
                self.root.clipboard_append(command)
                messagebox.showinfo("成功", "命令已复制到剪贴板！")
        else:
            messagebox.showwarning("警告", "没有可复制的命令！")
    
    def clear_settings(self):
        """清空所有设置"""
        # 清空文件相关变量
        self.file_var.set("")
        self.selected_files = []
        self.update_files_display()

        # 清空其他文本变量
        self.pages_var.set("")
        self.output_var.set("")
        self.openai_model_var.set(self.config.get('model', 'gpt-4o-mini'))
        self.openai_base_url_var.set(self.config.get('base_url', 'https://api.openai.com/v1'))
        self.openai_api_key_var.set(self.config.get('api', ''))
        self.max_pages_var.set("")
        self.qps_var.set("4")
        self.min_text_length_var.set("5")
        self.custom_prompt_var.set("")
        self.short_line_split_factor_var.set("0.8")
        
        # 重置布尔变量
        self.openai_var.set(True)
        self.enhance_compatibility_var.set(False)
        self.skip_clean_var.set(False)
        self.dual_translate_first_var.set(False)
        self.disable_rich_text_var.set(False)
        self.skip_scanned_detection_var.set(False)
        self.ocr_workaround_var.set(False)
        self.translate_table_text_var.set(False)
        self.no_dual_var.set(False)
        self.no_mono_var.set(False)
        self.ignore_cache_var.set(False)
        self.debug_var.set(False)
        self.split_short_lines_var.set(False)
        
        # 重置下拉框
        self.lang_in_var.set("en")
        self.lang_out_var.set("zh")
        self.watermark_var.set("watermarked")

def main():
    try:
        import pyperclip
    except ImportError:
        print("警告: 未安装 pyperclip 库，将使用系统剪贴板功能")
    
    root = tk.Tk()
    app = BabelDOCGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()